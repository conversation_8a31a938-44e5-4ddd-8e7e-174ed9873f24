<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataTables Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    
    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>DataTables Test - Fixed Version</h2>
        
        <!-- Debug Alert -->
        <div id="debug-alert" class="alert alert-info">
            <h5><i class="fa fa-info-circle"></i> Debug Information</h5>
            <div id="debug-content"></div>
        </div>
        
        <div id="error-alert" class="alert alert-danger" style="display: none;">
            <h5><i class="fa fa-exclamation-triangle"></i> Error Information</h5>
            <div id="error-content"></div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Daftar Pasien Baru</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="table-pasien-baru" class="table table-bordered w-100">
                                <thead>
                                    <tr style="background-color: #405189; color: white;">
                                        <th style="width: 30%;">No MR</th>
                                        <th style="width: 70%;">Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td>001</td><td>Ahmad Suryadi</td></tr>
                                    <tr><td>002</td><td>Siti Nurhaliza</td></tr>
                                    <tr><td>003</td><td>Budi Santoso</td></tr>
                                    <tr><td>004</td><td>Rina Sari</td></tr>
                                    <tr><td>005</td><td>Joko Widodo</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="table-notifikasi-kanker" class="table table-bordered w-100">
                                <thead>
                                    <tr style="background-color: #405189; color: white;">
                                        <th style="width: 30%;">No MR</th>
                                        <th style="width: 70%;">Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td>101</td><td>Maria Dewi</td></tr>
                                    <tr><td>102</td><td>Joko Widodo</td></tr>
                                    <tr><td>103</td><td>Rina Sari</td></tr>
                                    <tr><td>104</td><td>Agus Salim</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="table-registrasi-kanker" class="table table-bordered w-100">
                                <thead>
                                    <tr style="background-color: #405189; color: white;">
                                        <th style="width: 30%;">No MR</th>
                                        <th style="width: 70%;">Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td>201</td><td>Andi Pratama</td></tr>
                                    <tr><td>202</td><td>Lestari Wati</td></tr>
                                    <tr><td>203</td><td>Hendra Gunawan</td></tr>
                                    <tr><td>204</td><td>Sari Indah</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="table-bukan-kanker" class="table table-bordered w-100">
                                <thead>
                                    <tr style="background-color: #405189; color: white;">
                                        <th style="width: 30%;">No MR</th>
                                        <th style="width: 70%;">Nama</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td>301</td><td>Dewi Sartika</td></tr>
                                    <tr><td>302</td><td>Bambang Sutrisno</td></tr>
                                    <tr><td>303</td><td>Indira Sari</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Debug information
            console.log('=== DataTables Debug Information ===');
            console.log('jQuery version:', $.fn.jquery);
            console.log('DataTables available:', typeof $.fn.DataTable !== 'undefined');
            console.log('DataTables version:', $.fn.DataTable ? $.fn.DataTable.version : 'Not available');
            
            // Show debug info in alert
            var debugInfo = '<p><strong>jQuery Version:</strong> ' + $.fn.jquery + '</p>';
            debugInfo += '<p><strong>DataTables Available:</strong> ' + (typeof $.fn.DataTable !== 'undefined') + '</p>';
            debugInfo += '<p><strong>DataTables Version:</strong> ' + ($.fn.DataTable ? $.fn.DataTable.version : 'Not available') + '</p>';
            
            // Check if tables exist
            var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];
            tableIds.forEach(function(tableId) {
                var exists = $(tableId).length > 0;
                var isDataTable = $.fn.DataTable.isDataTable(tableId);
                console.log('Table ' + tableId + ' exists:', exists);
                console.log('Table ' + tableId + ' is DataTable:', isDataTable);
                debugInfo += '<p><strong>Table ' + tableId + ':</strong> Exists: ' + exists + ', IsDataTable: ' + isDataTable + '</p>';
            });
            
            $('#debug-content').html(debugInfo);

            // Fungsi untuk menghancurkan DataTable yang sudah ada
            function destroyExistingDataTables() {
                var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];
                
                tableIds.forEach(function(tableId) {
                    if ($.fn.DataTable.isDataTable(tableId)) {
                        $(tableId).DataTable().destroy();
                        console.log('Destroyed existing DataTable: ' + tableId);
                    }
                });
            }

            // Fungsi untuk inisialisasi DataTables
            function initializeDataTables() {
                try {
                    // Hancurkan DataTable yang sudah ada terlebih dahulu
                    destroyExistingDataTables();

                    var commonOptions = {
                        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        "pageLength": 10,
                        "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "Semua"]],
                        "pagingType": "full_numbers",
                        "responsive": true,
                        "autoWidth": false,
                        "destroy": true, // Tambahkan opsi destroy untuk mencegah error reinitialize
                        "language": {
                            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
                            "zeroRecords": "Tidak ada data yang ditemukan",
                            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
                            "infoFiltered": "(difilter dari _MAX_ total entri)",
                            "search": "Cari:",
                            "searchPlaceholder": "Ketik untuk mencari...",
                            "processing": "Sedang memproses...",
                            "paginate": {
                                "first": "Pertama",
                                "last": "Terakhir",
                                "next": "Berikutnya",
                                "previous": "Sebelumnya"
                            }
                        }
                    };

                    // Inisialisasi semua tabel dengan opsi yang sama
                    var tables = [
                        '#table-pasien-baru',
                        '#table-notifikasi-kanker', 
                        '#table-registrasi-kanker',
                        '#table-bukan-kanker'
                    ];

                    var successCount = 0;
                    tables.forEach(function(tableId) {
                        try {
                            if ($(tableId).length > 0) {
                                $(tableId).DataTable(commonOptions);
                                console.log('Successfully initialized DataTable: ' + tableId);
                                successCount++;
                            } else {
                                console.warn('Table not found: ' + tableId);
                            }
                        } catch (error) {
                            console.error('Error initializing DataTable ' + tableId + ':', error);
                            $('#error-content').append('<p><strong>Error with ' + tableId + ':</strong> ' + error.message + '</p>');
                            $('#error-alert').show();
                        }
                    });

                    // Update debug info with success count
                    $('#debug-content').append('<p><strong>Initialization Result:</strong> Successfully initialized ' + successCount + ' out of ' + tables.length + ' tables</p>');

                    console.log('All DataTables initialized successfully');

                } catch (error) {
                    console.error('Error in DataTables initialization:', error);
                    $('#error-content').append('<p><strong>General Error:</strong> ' + error.message + '</p>');
                    $('#error-alert').show();
                }
            }

            // Tunggu sampai DOM dan semua script selesai dimuat
            setTimeout(function() {
                initializeDataTables();
            }, 100);
        });
    </script>
</body>
</html>
