@extends('layouts.app')

@section('title', 'Data Regkan')

@push('styles')
    <style>
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .table-responsive {
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        /* Specific styling for Data Regkan tables */
        .card-body .table-responsive {
            min-height: 400px;
        }

        #table-pasien-baru_wrapper,
        #table-notifikasi-kanker_wrapper,
        #table-registrasi-kanker_wrapper,
        #table-bukan-kanker_wrapper {
            padding: 0;
        }

        /* Ensure consistent table styling */
        .table th {
            font-weight: 600;
            font-size: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            font-size: 0.875rem;
            vertical-align: middle;
        }

        /* Compact DataTable Pagination Styling */
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 0.25rem 0.5rem !important;
            margin: 0 0.1rem !important;
            font-size: 0.875rem !important;
            min-width: auto !important;
            border-radius: 0.25rem !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #405189 !important;
            color: white !important;
            border-color: #405189 !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #405189 !important;
        }

        .dataTables_wrapper .dataTables_info {
            font-size: 0.875rem;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length {
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length select {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .dataTables_wrapper .dataTables_filter input {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Compact pagination for smaller screens */
        @media (max-width: 768px) {
            .dataTables_wrapper .dataTables_paginate .paginate_button {
                padding: 0.2rem 0.4rem !important;
                margin: 0 0.05rem !important;
                font-size: 0.8rem !important;
            }

            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter {
                font-size: 0.8rem;
            }
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .card-body .table-responsive {
                min-height: 300px;
            }

            .table th,
            .table td {
                font-size: 0.8rem;
                padding: 0.5rem 0.25rem;
            }
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        {{-- Daftar Pasien Baru --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Baru</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-pasien-baru" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarPasienBaru as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Daftar Pasien Notifikasi Kanker --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-notifikasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarPasienNotifikasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Daftar Pasien Registrasi Kanker --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-registrasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarPasienRegistrasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        {{-- Daftar Bukan Registrasi Kanker --}}
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-bukan-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($daftarBukanRegistrasiKanker as $pasien)
                                    <tr>
                                        <td>{{ $pasien['no_mr'] }}</td>
                                        <td>{{ $pasien['nama'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Fungsi untuk menghancurkan DataTable yang sudah ada
            function destroyExistingDataTables() {
                var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];

                tableIds.forEach(function(tableId) {
                    if ($.fn.DataTable.isDataTable(tableId)) {
                        $(tableId).DataTable().destroy();
                        console.log('Destroyed existing DataTable: ' + tableId);
                    }
                });
            }

            // Fungsi untuk inisialisasi DataTables
            function initializeDataTables() {
                try {
                    // Hancurkan DataTable yang sudah ada terlebih dahulu
                    destroyExistingDataTables();

                    var commonOptions = {
                        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        "pageLength": 10,
                        "lengthMenu": [[5, 10, 25, 50], [5, 10, 25, 50]],
                        "pagingType": "simple_numbers", // Changed from full_numbers to simple_numbers for more compact pagination
                        "responsive": true,
                        "autoWidth": false,
                        "destroy": true, // Tambahkan opsi destroy untuk mencegah error reinitialize
                        "language": {
                            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
                            "zeroRecords": "Tidak ada data yang ditemukan",
                            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
                            "infoFiltered": "(difilter dari _MAX_ total entri)",
                            "search": "Cari:",
                            "searchPlaceholder": "Ketik untuk mencari...",
                            "processing": "Sedang memproses...",
                            "paginate": {
                                "first": "Pertama",
                                "last": "Terakhir",
                                "next": "Berikutnya",
                                "previous": "Sebelumnya"
                            },
                            "aria": {
                                "sortAscending": ": aktifkan untuk mengurutkan kolom secara ascending",
                                "sortDescending": ": aktifkan untuk mengurutkan kolom secara descending"
                            }
                        },
                        "drawCallback": function(settings) {
                            // Pastikan pagination styling diterapkan setelah draw
                            $('.dataTables_paginate .paginate_button').removeClass('page-link');
                            $('.dataTables_paginate .pagination').removeClass('pagination');

                            // Apply compact styling to pagination
                            $('.dataTables_paginate').addClass('compact-pagination');

                            // Hide ellipsis if there are too many pages for mobile
                            if ($(window).width() < 768) {
                                $('.dataTables_paginate .paginate_button').each(function() {
                                    if ($(this).text() === '…') {
                                        $(this).hide();
                                    }
                                });
                            }
                        }
                    };

                    // Inisialisasi semua tabel dengan opsi yang sama
                    var tables = [
                        '#table-pasien-baru',
                        '#table-notifikasi-kanker',
                        '#table-registrasi-kanker',
                        '#table-bukan-kanker'
                    ];

                    tables.forEach(function(tableId) {
                        try {
                            if ($(tableId).length > 0) {
                                $(tableId).DataTable(commonOptions);
                                console.log('Successfully initialized DataTable: ' + tableId);
                            } else {
                                console.warn('Table not found: ' + tableId);
                            }
                        } catch (error) {
                            console.error('Error initializing DataTable ' + tableId + ':', error);
                        }
                    });

                    // Responsive handling untuk mobile
                    $(window).off('resize.dataregkan').on('resize.dataregkan', function() {
                        $('.dataTables_wrapper').each(function() {
                            try {
                                var table = $(this).find('table').DataTable();
                                if (table) {
                                    table.columns.adjust().responsive.recalc();
                                }
                            } catch (error) {
                                console.warn('Error adjusting table on resize:', error);
                            }
                        });
                    });

                    console.log('All DataTables initialized successfully');

                } catch (error) {
                    console.error('Error in DataTables initialization:', error);
                }
            }

            // Tunggu sampai DOM dan semua script selesai dimuat
            setTimeout(function() {
                initializeDataTables();
            }, 100);

            // Cleanup saat halaman akan ditinggalkan
            $(window).on('beforeunload', function() {
                destroyExistingDataTables();
            });
        });
    </script>
@endpush
