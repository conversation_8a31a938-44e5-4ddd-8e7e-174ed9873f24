

<?php $__env->startSection('title', 'Data Regkan'); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .table-responsive {
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        /* Specific styling for Data Regkan tables */
        .card-body .table-responsive {
            min-height: 400px;
        }

        #table-pasien-baru_wrapper,
        #table-notifikasi-kanker_wrapper,
        #table-registrasi-kanker_wrapper,
        #table-bukan-kanker_wrapper {
            padding: 0;
        }

        /* Ensure consistent table styling */
        .table th {
            font-weight: 600;
            font-size: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            font-size: 0.875rem;
            vertical-align: middle;
        }

        /* Compact DataTable Pagination Styling */
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 0.25rem 0.5rem !important;
            margin: 0 0.1rem !important;
            font-size: 0.875rem !important;
            min-width: auto !important;
            border-radius: 0.25rem !important;
        }

        /* Active/Current page styling with higher specificity */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:focus,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:active,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current.disabled {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border: 1px solid #405189 !important;
            border-color: #405189 !important;
            font-weight: 600 !important;
            box-shadow: 0 2px 4px rgba(64, 81, 137, 0.2) !important;
        }

        /* Hover state for non-current buttons */
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #405189 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        /* Ensure current page styling overrides hover */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            box-shadow: 0 2px 6px rgba(64, 81, 137, 0.3) !important;
        }

        .dataTables_wrapper .dataTables_info {
            font-size: 0.875rem;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length {
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length select {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .dataTables_wrapper .dataTables_filter input {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Additional compact pagination styling */
        .compact-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.1rem;
        }

        .compact-pagination .paginate_button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 2rem;
            border: 1px solid #dee2e6;
            background-color: #ffffff;
            color: #495057;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
        }

        /* Fallback current page styling with maximum specificity */
        .dataTables_wrapper .compact-pagination .paginate_button.current,
        .dataTables_wrapper .compact-pagination .paginate_button[aria-current="page"] {
            background-color: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            font-weight: 600 !important;
            position: relative;
        }

        /* Add a subtle indicator for current page */
        .dataTables_wrapper .compact-pagination .paginate_button.current::before,
        .dataTables_wrapper .compact-pagination .paginate_button[aria-current="page"]::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background-color: #ffffff;
            border-radius: 1px;
        }

        /* Disabled button styling */
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            background: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
            box-shadow: none !important;
        }

        /* Compact pagination for smaller screens */
        @media (max-width: 768px) {
            .dataTables_wrapper .dataTables_paginate .paginate_button {
                padding: 0.2rem 0.4rem !important;
                margin: 0 0.05rem !important;
                font-size: 0.8rem !important;
            }

            /* Mobile current page styling */
            .dataTables_wrapper .dataTables_paginate .paginate_button.current,
            .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
                background-color: #405189 !important;
                color: #ffffff !important;
                border-color: #405189 !important;
                font-weight: 600 !important;
                box-shadow: 0 1px 3px rgba(64, 81, 137, 0.3) !important;
            }

            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter {
                font-size: 0.8rem;
            }
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .card-body .table-responsive {
                min-height: 300px;
            }

            .table th,
            .table td {
                font-size: 0.8rem;
                padding: 0.5rem 0.25rem;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Baru</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-pasien-baru" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarPasienBaru; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-notifikasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarPasienNotifikasiKanker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-registrasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarPasienRegistrasiKanker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-bukan-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarBukanRegistrasiKanker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Fungsi untuk menghancurkan DataTable yang sudah ada
            function destroyExistingDataTables() {
                var tableIds = ['#table-pasien-baru', '#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];

                tableIds.forEach(function(tableId) {
                    if ($.fn.DataTable.isDataTable(tableId)) {
                        $(tableId).DataTable().destroy();
                        console.log('Destroyed existing DataTable: ' + tableId);
                    }
                });
            }

            // Fungsi untuk inisialisasi DataTables
            function initializeDataTables() {
                try {
                    // Hancurkan DataTable yang sudah ada terlebih dahulu
                    destroyExistingDataTables();

                    var commonOptions = {
                        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        "pageLength": 10,
                        "lengthMenu": [[5, 10, 25, 50], [5, 10, 25, 50]],
                        "pagingType": "simple_numbers", // Changed from full_numbers to simple_numbers for more compact pagination
                        "responsive": true,
                        "autoWidth": false,
                        "destroy": true, // Tambahkan opsi destroy untuk mencegah error reinitialize
                        "language": {
                            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
                            "zeroRecords": "Tidak ada data yang ditemukan",
                            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
                            "infoFiltered": "(difilter dari _MAX_ total entri)",
                            "search": "Cari:",
                            "searchPlaceholder": "Ketik untuk mencari...",
                            "processing": "Sedang memproses...",
                            "paginate": {
                                "first": "Pertama",
                                "last": "Terakhir",
                                "next": "Berikutnya",
                                "previous": "Sebelumnya"
                            },
                            "aria": {
                                "sortAscending": ": aktifkan untuk mengurutkan kolom secara ascending",
                                "sortDescending": ": aktifkan untuk mengurutkan kolom secara descending"
                            }
                        },
                        "drawCallback": function(settings) {
                            // Remove Bootstrap classes that might interfere
                            $('.dataTables_paginate .paginate_button').removeClass('page-link');
                            $('.dataTables_paginate .pagination').removeClass('pagination');

                            // Apply compact styling to pagination
                            $('.dataTables_paginate').addClass('compact-pagination');

                            // Ensure current page styling is properly applied
                            $('.dataTables_paginate .paginate_button.current').each(function() {
                                $(this).css({
                                    'background-color': '#405189',
                                    'color': '#ffffff',
                                    'border-color': '#405189',
                                    'font-weight': '600'
                                });
                            });

                            // Style non-current buttons
                            $('.dataTables_paginate .paginate_button:not(.current):not(.disabled)').each(function() {
                                $(this).css({
                                    'background-color': '#ffffff',
                                    'color': '#495057',
                                    'border-color': '#dee2e6'
                                });
                            });

                            // Hide ellipsis if there are too many pages for mobile
                            if ($(window).width() < 768) {
                                $('.dataTables_paginate .paginate_button').each(function() {
                                    if ($(this).text() === '…') {
                                        $(this).hide();
                                    }
                                });
                            }

                            // Add click event to maintain styling after page change
                            $('.dataTables_paginate .paginate_button:not(.disabled)').off('click.customPagination').on('click.customPagination', function() {
                                setTimeout(function() {
                                    // Re-apply current page styling after DataTable processes the click
                                    $('.dataTables_paginate .paginate_button.current').css({
                                        'background-color': '#405189',
                                        'color': '#ffffff',
                                        'border-color': '#405189',
                                        'font-weight': '600'
                                    });
                                }, 50);
                            });
                        }
                    };

                    // Inisialisasi semua tabel dengan opsi yang sama
                    var tables = [
                        '#table-pasien-baru',
                        '#table-notifikasi-kanker',
                        '#table-registrasi-kanker',
                        '#table-bukan-kanker'
                    ];

                    tables.forEach(function(tableId) {
                        try {
                            if ($(tableId).length > 0) {
                                $(tableId).DataTable(commonOptions);
                                console.log('Successfully initialized DataTable: ' + tableId);
                            } else {
                                console.warn('Table not found: ' + tableId);
                            }
                        } catch (error) {
                            console.error('Error initializing DataTable ' + tableId + ':', error);
                        }
                    });

                    // Responsive handling untuk mobile
                    $(window).off('resize.dataregkan').on('resize.dataregkan', function() {
                        $('.dataTables_wrapper').each(function() {
                            try {
                                var table = $(this).find('table').DataTable();
                                if (table) {
                                    table.columns.adjust().responsive.recalc();
                                }
                            } catch (error) {
                                console.warn('Error adjusting table on resize:', error);
                            }
                        });
                    });

                    console.log('All DataTables initialized successfully');

                } catch (error) {
                    console.error('Error in DataTables initialization:', error);
                }
            }

            // Tunggu sampai DOM dan semua script selesai dimuat
            setTimeout(function() {
                initializeDataTables();
            }, 100);

            // Cleanup saat halaman akan ditinggalkan
            $(window).on('beforeunload', function() {
                destroyExistingDataTables();
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\regkan\resources\views/data-regkan/index.blade.php ENDPATH**/ ?>